实验1 图像灰度变换与直方图处理
一、实验目的
理解像素灰度值、直方图概念；掌握直方图均衡化原理及实现。理解像素灰度值、直方图概念；掌握直方图均衡化原理及实现。
二、实验内容
1.读取灰度图像，显示图像及其直方图。
2.实现：线性变换（对比度拉伸、负片）、对数变换、幂律（伽马）变换。
3.实现：全局直方图均衡化。对比处理前后图像及直方图变化，理解均衡化的作用。
三、实验结果与分析
1. 对比度拉伸
函数定义：
python
# 线性变换 - 对比度拉伸
def contrast_stretching(image, r_min, r_max, s_min, s_max):
    """
    对图像进行对比度拉伸.
    :param image: 输入图像 (numpy array)
    :param r_min: 输入图像的最小灰度值
    :param r_max: 输入图像的最大灰度值
    :param s_min: 输出图像的最小灰度值
    :param s_max: 输出图像的最大灰度值
    :return: 处理后的图像 (numpy array)
    image_float = image.astype(np.float64)
    
    # 应用线性变换公式: s = (r - r_min) / (r_max - r_min) * (s_max - s_min) + s_min
    stretched = ((image_float - r_min) / (r_max - r_min)) * (s_max - s_min) + s_min
    
    # 使用 np.clip 将像素值限制在 [0, 255] 范围内，防止溢出
    # 然后将图像转换回 uint8 类型
    return np.uint8(np.clip(stretched, 0, 255))
调用代码：
python
# 获取图像灰度值的最小和最大值
r_min, r_max = np.min(image), np.max(image)
# 应用对比度拉伸
stretched_image = contrast_stretching(image, r_min, r_max, 0, 255)
结果图像与分析：
[对比度拉伸结果](images/contrast_stretching.png "对比度拉伸前后对比")
 
图1-1：对比度拉伸前后图像及直方图对比*
结果: 变换后的图像对比度明显增强，视觉上更加清晰。
分析: 原始图像的灰度值可能集中在一个较窄的范围内，导致图像看起来"灰蒙蒙"的。对比度拉伸将这个窄范围扩展到整个 [0, 255] 的灰度空间。从直方图上看，变换后的直方图覆盖了更宽的灰度范围，分布也相对更均匀。
2. 负片变换
函数定义：
python
# 线性变换 - 负片
def negative(image):
    对图像进行负片变换.
    :param image: 输入图像 (numpy array)
    :return: 处理后的图像 (numpy array)
    # 变换公式 s = L - 1 - r, 其中 L-1 为 255
    return 255 - image
调用代码：
python
negative_image = negative(image)
结果图像与分析：
[负片变换结果](images/negative.png "负片变换前后对比")
 
*图1-2：负片变换前后图像及直方图对比*
结果: 图像呈现出底片效果，亮的区域变暗，暗的区域变亮。
分析: 该变换是简单的灰度反转。其直方图是原始直方图相对于中心灰度值（127.5）的镜像。
3. 对数变换
函数定义：
python
#对数变换
def log_transform(image, c):
    对图像进行对数变换.
    :param image: 输入图像 (numpy array)
    :param c: 变换常数, 控制动态范围的扩展程度
    :return: 处理后的图像 (numpy array)
    # 为保证 log(1+r) 的计算精度，先转换为浮点数
    image_float = image.astype(np.float64)
    # 应用对数变换公式 s = c * log(1 + r)
    # 使用 np.log1p(x) 代替 np.log(1 + x) 以获得更高的精度
    log_transformed = c * np.log1p(image_float)
    # 将像素值裁剪到 [0, 255] 并转回 uint8
    return np.uint8(np.clip(log_transformed, 0, 255))
调用代码：
python
# c值可以调整变换的强度
log_image = log_transform(image, c=40)
结果图像与分析：
[对数变换结果](images/log_transform.png "对数变换前后对比")
 
*图1-3：对数变换前后图像及直方图对比*
结果: 图像暗部区域的细节变得更加清晰可见，整体亮度有所提升。
分析: 对数变换扩展了低灰度值区域，压缩了高灰度值区域。因此，对于暗部细节丰富但整体偏暗的图像，该变换能有效增强其可视性。其直方图的低灰度部分被拉伸，向高灰度区域（右侧）延展。
4. 幂律（伽马）变换
函数定义：
python
# 幂律（伽马）变换
def gamma_correction(image, gamma):
    对图像进行幂律（伽马）变换.
    :param image: 输入图像 (numpy array)
    :param gamma: 伽马值
    :return: 处理后的图像 (numpy array)
    # 将像素值归一化到 [0, 1] 范围以便进行幂运算
    image_float = image.astype(np.float64) / 255
    # 应用幂律变换公式 s = c * r^gamma, 这里 c=1 (因为已经归一化)
    # 然后将结果映射回 [0, 255]
    gamma_corrected = 255 * (image_float ** gamma)
    
    # 将像素值裁剪到 [0, 255] 并转回 uint8
    return np.uint8(np.clip(gamma_corrected, 0, 255))
调用代码：
python
gamma值可以调整变换的曲线
gamma_image = gamma_correction(image, gamma=2.2)
结果图像与分析：
 [伽马变换结果](images/gamma_correction.png "伽马变换前后对比")
 
*图1-4：幂律（伽马）变换前后图像及直方图对比*

结果: 由于实验中设置 `γ = 2.2 > 1`，图像整体变暗。
分析: 当 `γ > 1` 时，幂律变换会压缩高灰度区（亮区），扩展低灰度区（暗区），导致图像整体色调变暗。此操作常用于伽马校正。从直方图上看，整体分布会向低灰度区域（左侧）偏移和集中。
5. 直方图均衡化
函数定义：
```python
# 全局直方图均衡化
def histogram_equalization(image):
    对图像进行全局直方图均衡化.
    :param image: 输入图像 (numpy array), 必须是8位单通道图像
    :return: 处理后的图像 (numpy array)
     cv2.equalizeHist 实现了直方图均衡化算法:
     1. 计算图像的灰度直方图
     2. 计算直方图的累积分布函数 (CDF)
     3. 根据 CDF 将原始灰度值映射到新的灰度值
    return cv2.equalizeHist(image)
调用代码：
python
equalized_image = histogram_equalization(image)
结果图像与分析：
[直方图均衡化结果](images/histogram_equalization.png "直方图均衡化前后对比
 
*图1-5：直方图均衡化前后图像及直方图对比*
结果: 图像的全局对比度得到显著提升，画面中原先不易分辨的细节变得更加突出。
分析: 直方图均衡化通过灰度值的重新映射，使得变换后图像的灰度直方图趋于均匀分布。这意味着图像的动态范围得到了充分利用，有效地增强了图像的整体对比度，特别适用于那些像素值集中在某个狭窄区间的图像。
6.实验总结
1.  灰度变换是图像增强的基础：本实验探讨的各种灰度变换方法，都是通过点对点的灰度映射函数来改变图像像素值，以改善图像的视觉效果或突出特定信息。
2.  不同变换适用于不同场景：
 对比度拉伸和直方图均衡化都能有效提升全局对比度。对比度拉伸是线性的，而直方图均衡化是非线性的，通常能更显著地增强对比度。
对数变换主要用于增强图像的暗部细节，它扩展了低灰度级范围。
 幂律（伽马）变换则更为灵活，通过调整伽马值可以有选择性地增强暗部（gamma < 1）或亮部（gamma > 1）的对比度。
 负片变换则是一种简单的反色操作，适用于观察嵌入在暗背景中的白色或灰色细节。
3.  变换选择的重要性：没有一种变换是万能的。选择哪种灰度变换方法，取决于原始图像的特性以及图像增强的具体目标。

